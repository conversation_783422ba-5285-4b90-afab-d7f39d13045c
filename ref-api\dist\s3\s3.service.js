"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3Service = void 0;
const common_1 = require("@nestjs/common");
const client_s3_1 = require("@aws-sdk/client-s3");
let S3Service = class S3Service {
    s3Client;
    constructor() {
        this.s3Client = new client_s3_1.S3Client({
            endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
            region: 'us-east-1',
            credentials: {
                accessKeyId: process.env.S3_ACCESS_KEY || 'minio',
                secretAccessKey: process.env.S3_SECRET_KEY || 'minio123',
            },
            forcePathStyle: true,
        });
    }
    async uploadFile(bucket, key, body, contentType) {
        const command = new client_s3_1.PutObjectCommand({
            Bucket: bucket,
            Key: key,
            Body: body,
            ContentType: contentType,
        });
        await this.s3Client.send(command);
    }
    async getFile(bucket, key) {
        const command = new client_s3_1.GetObjectCommand({
            Bucket: bucket,
            Key: key,
        });
        return this.s3Client.send(command);
    }
    async deleteFile(bucket, key) {
        const command = new client_s3_1.DeleteObjectCommand({
            Bucket: bucket,
            Key: key,
        });
        await this.s3Client.send(command);
    }
    getClient() {
        return this.s3Client;
    }
};
exports.S3Service = S3Service;
exports.S3Service = S3Service = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], S3Service);
//# sourceMappingURL=s3.service.js.map