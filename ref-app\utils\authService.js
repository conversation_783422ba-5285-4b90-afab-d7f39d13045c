import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  signInWithCredential,
  updateProfile,
  sendPasswordResetEmail,
  sendEmailVerification,
} from 'firebase/auth';
import { auth } from '../firebaseConfig';

// Auth Service Class
class AuthService {
  constructor() {
    this.auth = auth;
    this.currentUser = null;
    this.authStateListeners = [];
  }

  // Get current user
  getCurrentUser() {
    return this.auth.currentUser;
  }

  // Sign in with email and password
  async signInWithEmail(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      return {
        success: true,
        user: userCredential.user,
        message: 'Successfully signed in'
      };
    } catch (error) {
      return {
        success: false,
        error: error.code,
        message: this.getErrorMessage(error.code)
      };
    }
  }

  // Create account with email and password
  async createAccount(email, password, displayName = null) {
    try {
      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);

      // Update display name if provided
      if (displayName) {
        await updateProfile(userCredential.user, { displayName: displayName });
      }

      // Send email verification
      await sendEmailVerification(userCredential.user);

      return {
        success: true,
        user: userCredential.user,
        message: 'Account created successfully. Please check your email for verification.'
      };
    } catch (error) {
      return {
        success: false,
        error: error.code,
        message: this.getErrorMessage(error.code)
      };
    }
  }

  // Sign in with Google (requires additional setup)
  async signInWithGoogle(googleCredential) {
    try {
      const credential = GoogleAuthProvider.credential(googleCredential.idToken);
      const userCredential = await signInWithCredential(this.auth, credential);
      
      return {
        success: true,
        user: userCredential.user,
        message: 'Successfully signed in with Google'
      };
    } catch (error) {
      return {
        success: false,
        error: error.code,
        message: this.getErrorMessage(error.code)
      };
    }
  }

  // Sign out
  async signOut() {
    try {
      await signOut(this.auth);
      return {
        success: true,
        message: 'Successfully signed out'
      };
    } catch (error) {
      return {
        success: false,
        error: error.code,
        message: this.getErrorMessage(error.code)
      };
    }
  }

  // Send password reset email
  async resetPassword(email) {
    try {
      await sendPasswordResetEmail(this.auth, email);
      return {
        success: true,
        message: 'Password reset email sent'
      };
    } catch (error) {
      return {
        success: false,
        error: error.code,
        message: this.getErrorMessage(error.code)
      };
    }
  }

  // Auth state listener
  onAuthStateChange(callback) {
    return onAuthStateChanged(this.auth, (user) => {
      this.currentUser = user;
      callback(user);
    });
  }

  // Helper method to get user-friendly error messages
  getErrorMessage(errorCode) {
    const errorMessages = {
      'auth/user-not-found': 'No account found with this email address.',
      'auth/wrong-password': 'Incorrect password.',
      'auth/email-already-in-use': 'An account with this email already exists.',
      'auth/weak-password': 'Password should be at least 6 characters.',
      'auth/invalid-email': 'Please enter a valid email address.',
      'auth/user-disabled': 'This account has been disabled.',
      'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
      'auth/network-request-failed': 'Network error. Please check your connection.',
      'auth/invalid-credential': 'Invalid credentials provided.',
      'auth/account-exists-with-different-credential': 'Account exists with different sign-in method.',
    };

    return errorMessages[errorCode] || 'An unexpected error occurred. Please try again.';
  }

  // Check if user email is verified
  isEmailVerified() {
    return this.auth.currentUser?.emailVerified || false;
  }

  // Resend email verification
  async resendEmailVerification() {
    try {
      if (this.auth.currentUser) {
        await sendEmailVerification(this.auth.currentUser);
        return {
          success: true,
          message: 'Verification email sent'
        };
      }
      throw new Error('No user signed in');
    } catch (error) {
      return {
        success: false,
        error: error.code || 'unknown',
        message: error.message || 'Failed to send verification email'
      };
    }
  }
}

// Export singleton instance
export default new AuthService();

// Named exports for individual functions if preferred
export {
  AuthService,
};
