import { PrismaService } from './prisma.service';
import { RedisService } from './redis/redis.service';
import { S3Service } from './s3/s3.service';
export declare class AppService {
    private prisma;
    private redis;
    private s3;
    constructor(prisma: PrismaService, redis: RedisService, s3: S3Service);
    getHello(): string;
    testDatabase(): Promise<string>;
    testRedis(): Promise<string>;
    testS3(): Promise<string>;
}
