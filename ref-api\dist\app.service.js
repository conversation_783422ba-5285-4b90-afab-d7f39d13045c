"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("./prisma.service");
const redis_service_1 = require("./redis/redis.service");
const s3_service_1 = require("./s3/s3.service");
let AppService = class AppService {
    prisma;
    redis;
    s3;
    constructor(prisma, redis, s3) {
        this.prisma = prisma;
        this.redis = redis;
        this.s3 = s3;
    }
    getHello() {
        return 'Hello World!';
    }
    async testDatabase() {
        try {
            await this.prisma.$queryRaw `SELECT 1`;
            return 'Database connection successful!';
        }
        catch (error) {
            return `Database connection failed: ${error.message}`;
        }
    }
    async testRedis() {
        try {
            await this.redis.set('test', 'Hello Redis!', 60);
            const value = await this.redis.get('test');
            return `Redis connection successful! Value: ${value}`;
        }
        catch (error) {
            return `Redis connection failed: ${error.message}`;
        }
    }
    async testS3() {
        try {
            const client = this.s3.getClient();
            return 'S3/MinIO client initialized successfully!';
        }
        catch (error) {
            return `S3/MinIO connection failed: ${error.message}`;
        }
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        redis_service_1.RedisService,
        s3_service_1.S3Service])
], AppService);
//# sourceMappingURL=app.service.js.map