import { AppService } from './app.service';
export declare class AppController {
    private readonly appService;
    constructor(appService: AppService);
    getHello(): string;
    getHealth(): {
        status: string;
        timestamp: string;
    };
    testDatabase(): Promise<{
        service: string;
        result: string;
    }>;
    testRedis(): Promise<{
        service: string;
        result: string;
    }>;
    testS3(): Promise<{
        service: string;
        result: string;
    }>;
}
